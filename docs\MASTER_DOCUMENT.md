# Project Manager Master Document

## Table of Contents

1. [Introduction](#introduction)
2. [Architecture Overview](#architecture-overview)
3. [MVC Implementation](#mvc-implementation)
4. [Node System](#node-system)
5. [Component System](#component-system)
6. [User Interface](#user-interface)
7. [Data Management](#data-management)
8. [Authentication and Authorization](#authentication-and-authorization)
9. [Project Management](#project-management)
10. [Hierarchical ID System](#hierarchical-id-system)
11. [Tag System](#tag-system)
12. [Search and Filtering](#search-and-filtering)
13. [Layout System](#layout-system)
14. [Interaction System](#interaction-system)
15. [Keyboard Shortcuts](#keyboard-shortcuts)
16. [Appendix: Feature Reference](#appendix-feature-reference)

## Introduction

Project Manager is a mind map-like project management tool that allows users to create, organize, and manage projects through a hierarchical node system. The application provides a flexible canvas where users can create nodes, add various components to them, and organize them in a hierarchical structure.

The application has undergone a significant architectural transition from a legacy system to a Model-View-Controller (MVC) architecture. This document serves as the comprehensive reference for the current state of the system, its architecture, components, and functionality.

## Architecture Overview

### Current Architecture

The application is built on a Model-View-Controller (MVC) architecture with a clear separation of concerns:

- **Model**: Represents the data and business logic
  - NodeStore: Central repository for all node data
  - EventEmitter: Handles event-based communication
  - StorageAdapter: Interface for data persistence

- **View**: Handles the presentation layer
  - React components for rendering the UI
  - Custom rendering pipeline for nodes and edges
  - Modal components for user interaction

- **Controller**: Manages the application logic
  - NodeController: Handles node operations
  - EdgeController: Handles edge operations
  - LayoutController: Manages node layout
  - CommandManager: Implements undo/redo functionality

### Key Architectural Principles

1. **Single Source of Truth**: GraphContext serves as the single source of truth for node data
2. **Event-Driven Architecture**: Changes to node data fire events to update all components
3. **Separation of Concerns**: Clear separation between model, view, and controller
4. **Centralized State Management**: NodeStore manages all node data
5. **Adapter Pattern**: StorageAdapter interface for data persistence

### Migration Status

The migration from the legacy system to the MVC architecture has been largely completed:

- The MVC architecture is now the primary system
- The legacy toggle in MVCPowerManager.tsx has been disabled (`const useMVC = true;`)
- App.tsx routes have been updated to redirect from legacy routes to MVC routes
- Legacy components still exist in the codebase but are disabled

For more details on the migration status, see [MVC_MIGRATION_GUIDE.md](MVC_MIGRATION_GUIDE.md).

## MVC Implementation

### Model Layer

The model layer consists of the following key components:

1. **NodeModel**: Represents a node in the graph
   - Encapsulates all data and behavior related to a node
   - Provides validation, serialization, and relationship management
   - Implements the Observer pattern for change notification

2. **NodeStore**: Central repository for all node data
   - Manages the collection of NodeModel instances
   - Provides CRUD operations for nodes
   - Emits events when nodes are added, updated, or removed

3. **EdgeModel**: Represents an edge between nodes
   - Encapsulates source and target node IDs
   - Provides validation and serialization

4. **EventEmitter**: Handles event-based communication
   - Implements the Observer pattern
   - Allows components to subscribe to and publish events

### View Layer

The view layer consists of the following key components:

1. **CustomNode**: Renders a node in the graph
   - Handles node appearance and interaction
   - Manages node buttons and hover states
   - Implements drag and drop functionality

2. **DirectNodeRenderer**: Renders the entire graph
   - Manages the canvas and viewport
   - Handles node positioning and scaling
   - Implements culling for performance optimization

3. **MVCNodeEditorModal**: Provides the node editing interface
   - Adapts the original NodeEditorModal to work with NodeModel
   - Handles component addition and removal
   - Manages node properties and relationships

### Controller Layer

The controller layer consists of the following key components:

1. **NodeController**: Handles node operations
   - Creates, updates, and deletes nodes
   - Manages node relationships
   - Implements node relinking functionality

2. **EdgeController**: Handles edge operations
   - Creates, updates, and deletes edges
   - Manages edge appearance and behavior

3. **LayoutController**: Manages node layout
   - Implements different layout algorithms
   - Handles automatic node positioning
   - Manages manual node positioning

4. **CommandManager**: Implements undo/redo functionality
   - Records commands for all operations
   - Provides undo and redo functionality
   - Maintains command history

For more details on the MVC implementation, see [MVC_IMPLEMENTATION_PLAN.md](MVC_IMPLEMENTATION_PLAN.md).

## Node System

### Node Structure

Nodes are the fundamental building blocks of the application. Each node has the following properties:

1. **Core Properties**:
   - id: Unique identifier
   - title: Node title
   - content: Node content
   - position: x, y coordinates
   - dimensions: width, height
   - color: Node color
   - type: Node type (richtext, tasks, deadline, etc.)

2. **Relationships**:
   - parentId: ID of the parent node
   - childIds: Array of child node IDs
   - siblingIndex: Position among siblings

3. **Visual Properties**:
   - completed: Whether the node is marked as complete
   - collapsed: Whether the node's children are hidden
   - manuallyPositioned: Whether the node has been manually positioned
   - label: Human-readable label (e.g., "Root-1-2")
   - hierarchicalId: Hierarchical identifier for addressing

4. **Components**:
   - segments: Array of node components (richtext, tasks, deadline, etc.)
   - tags: Array of tag IDs associated with the node

### Node Types

The application supports the following node types:

1. **Regular Node**: Standard node with title and components
2. **Title Node**: Oval-shaped node with only title text
3. **Root Node**: Special node that serves as the root of the graph

### Node Buttons

Nodes have the following buttons:

1. **Add Button**: Located at the bottom center, outside the node border
   - Creates a new child node
   - Appears on hover

2. **Delete Button**: Located at the bottom right, inside the node
   - Deletes the node
   - Shows a confirmation dialog
   - Appears on hover

3. **Complete Button**: Located at the bottom left, inside the node
   - Toggles the node's completion state
   - Appears on hover, remains visible when completed

4. **Color Button**: Located at the bottom left, next to the complete button
   - Opens the color picker
   - Appears on hover

5. **Collapse Button**: Located on the left side of the header
   - Toggles the node's collapsed state
   - Only appears for nodes with children

For more details on the node button system, see [NODE_BUTTON_RENDERING_GUIDE.md](NODE_BUTTON_RENDERING_GUIDE.md) and [BUTTON_SYSTEM_ANALYSIS.md](BUTTON_SYSTEM_ANALYSIS.md).

### Node Interaction

Nodes support the following interactions:

1. **Selection**: Click on a node to select it
2. **Editing**: Double-click or click and hold briefly to open the editor
3. **Dragging**: Click and hold to drag the node
4. **Relinking**: Drag a node onto another node to relink it
5. **Completion**: Click the complete button to mark as complete
6. **Color Change**: Click the color button to change the color
7. **Adding Children**: Click the add button to add a child node
8. **Deletion**: Click the delete button to delete the node

## Component System

### Component Types

The application supports the following component types:

1. **Rich Text**: Text editor with formatting options
2. **Tasks**: Checklist with completion tracking
3. **Deadline**: Date-based deadline with progress tracking
4. **Reminder**: Time-based reminder with recurrence options
5. **Table**: Structured data in tabular format
6. **Image**: Image upload and display

Each component type is implemented as a segment in the node's segments array. The demo-template.json file contains examples of all component types and their properties.

### Component Implementation

Components are implemented using the following pattern:

1. **Component Model**: Defines the data structure and validation
2. **Component View**: Renders the component in the node
3. **Component Editor**: Provides the editing interface in the node editor

## User Interface

### Main Views

The application has the following main views:

1. **Graph View**: The primary view showing the node graph
2. **Kanban View**: Alternative view showing nodes organized by tags
3. **Node Editor**: Modal for editing node properties and components
4. **Project Selection**: Interface for selecting and managing projects
5. **Settings**: User and application settings

### UI Components

Key UI components include:

1. **Toolbar**: Contains buttons for common actions
2. **Quick Search**: Persistent bubble at the top for searching
3. **Tag Filter**: Interface for filtering nodes by tags
4. **Center View Button**: Button to center the view on the graph
5. **Layout Selector**: Dropdown for selecting the graph layout

## Data Management

### Storage Adapters

The application uses the StorageAdapter interface for data persistence:

1. **SupabaseStorageAdapter**: Stores data in Supabase
   - Implements auto-save after 2 seconds of inactivity
   - Handles authentication and authorization
   - Manages project data

2. **LocalStorageAdapter**: Stores data in browser local storage
   - Used as a backup mechanism
   - Provides offline functionality
   - Handles dry run mode

### Data Flow

The data flow in the application follows this pattern:

1. User interacts with the UI
2. Controller processes the interaction
3. Model updates the data
4. Model emits events
5. View listens for events and updates the UI
6. StorageAdapter persists the changes

### Import/Export

The application supports importing and exporting projects:

1. **Export**: Exports the current project to a JSON file
   - Includes all nodes, edges, and tags
   - Stores images as base64

2. **Import**: Imports a project from a JSON file
   - Validates the imported data
   - Converts to the current data model
   - Updates the graph

## Authentication and Authorization

### Authentication Methods

The application supports the following authentication methods:

1. **Email/Password**: Traditional email and password login
2. **Magic Link**: Passwordless login via email link
3. **OAuth Providers**: Login via third-party providers (Google, GitHub, etc.)

### User Tiers

The application has the following user tiers:

1. **Tier 0 (Dry Run)**: No authentication, data stored in local storage only
2. **Tier 1 (Free)**: Basic features, limited to one project
3. **Tier 2 (Premium)**: All features, multiple projects, collaboration

### Authorization

The application implements the following authorization controls:

1. **Project Access**: Users can only access their own projects
2. **Collaboration**: Project owners can invite others to collaborate
3. **Feature Access**: Features are restricted based on user tier

## Project Management

### Project Structure

Projects are the top-level organizational unit:

1. **Project Properties**:
   - id: Unique identifier
   - name: Project name
   - description: Project description
   - createdAt: Creation timestamp
   - updatedAt: Last update timestamp
   - ownerId: ID of the project owner

2. **Project Data**:
   - nodes: Array of nodes in the project
   - edges: Array of edges in the project
   - tags: Array of tags in the project

### Project Operations

The application supports the following project operations:

1. **Create Project**: Creates a new empty project
2. **Load Project**: Loads an existing project
3. **Save Project**: Saves changes to the current project
4. **Delete Project**: Deletes a project
5. **Export Project**: Exports a project to a file
6. **Import Project**: Imports a project from a file
7. **Share Project**: Shares a project with other users

## Hierarchical ID System

### ID Structure

The application uses a hierarchical ID system for node addressing:

1. **Root Node**: "0"
2. **First Child**: "0-1"
3. **Second Child**: "0-2"
4. **First Grandchild of First Child**: "0-1-1"

### ID Management

The hierarchical ID system is managed as follows:

1. **Assignment**: IDs are assigned based on the node's position in the hierarchy
2. **Update**: IDs are updated when nodes are relinked
3. **Validation**: IDs are validated to ensure they follow the hierarchical pattern
4. **Resolution**: Invalid IDs are resolved by placing nodes under their closest matching parent

### ID History

The application maintains a history of ID changes:

1. **Tracking**: Changes to hierarchical IDs are tracked
2. **Reference Integrity**: References to nodes are updated when IDs change
3. **Circular References**: Circular references are handled by returning the last known state

## Tag System

### Tag Structure

Tags are used to categorize and filter nodes:

1. **Tag Properties**:
   - id: Unique identifier
   - name: Tag name
   - color: Tag color

2. **Default Tags**:
   - TODO: Orange
   - DOING: Blue
   - COMPLETED: Green
   - BUG: Red

### Tag Operations

The application supports the following tag operations:

1. **Create Tag**: Creates a new tag
2. **Update Tag**: Updates an existing tag
3. **Delete Tag**: Deletes a tag
4. **Assign Tag**: Assigns a tag to a node
5. **Remove Tag**: Removes a tag from a node
6. **Filter by Tag**: Filters nodes by tag

## Search and Filtering

### Quick Search

The application provides a quick search feature:

1. **Text Search**: Searches node titles and content
2. **Component Filter**: Filters nodes by component type using //component syntax
3. **Recent Filter**: Shows recently modified nodes using //recent syntax
4. **Tag Filter**: Filters nodes by tag

### Advanced Filtering

The application supports advanced filtering options:

1. **Completion Status**: Filters nodes by completion status
2. **Node Type**: Filters nodes by type
3. **Component Type**: Filters nodes by component type
4. **Tag Combination**: Filters nodes by multiple tags

## Layout System

### Layout Types

The application supports the following layout types:

1. **Top-Down**: Nodes are arranged vertically with parents above children
2. **Left-Right**: Nodes are arranged horizontally with parents to the left of children
3. **Right-Left**: Nodes are arranged horizontally with parents to the right of children
4. **Bottom-Up**: Nodes are arranged vertically with parents below children
5. **Force-Directed**: Nodes are arranged using a force-directed algorithm

### Layout Options

The application provides the following layout options:

1. **Manual Positioning**: Nodes can be manually positioned
2. **Auto Layout**: Nodes are automatically arranged based on the selected layout
3. **Compact Layout**: Nodes are arranged more compactly
4. **Spacing**: Controls the spacing between nodes

## Interaction System

### Mouse Interactions

The application supports the following mouse interactions:

1. **Click**: Selects a node
2. **Double-Click**: Opens the node editor
3. **Click and Hold**: Initiates dragging
4. **Drag**: Moves a node
5. **Drag and Drop**: Relinks a node
6. **Scroll**: Zooms in and out
   - Continuous zooming triggers node opening when threshold is reached
   - Supports both delta accumulation and time-based thresholds
   - Adapts sensitivity based on input device (touchpad vs. mouse wheel)
   - Provides visual feedback with progress indicator
7. **Middle-Click Drag**: Pans the canvas

### Touch Interactions

The application supports the following touch interactions:

1. **Tap**: Selects a node
2. **Double-Tap**: Opens the node editor
3. **Long Press**: Initiates dragging
4. **Drag**: Moves a node
5. **Pinch**: Zooms in and out
6. **Two-Finger Drag**: Pans the canvas

## Node Dragging System

The node dragging system is a sophisticated multi-component architecture that provides real-time, responsive node movement with proper coordinate transformations and layout integration.

### Architecture Overview

The node dragging system consists of several key components working together:

1. **ViewInteractionLayer**: Primary event capture and drag state management
2. **NodeController**: Business logic for drag operations
3. **MVCGraphLayoutService**: Layout integration and drag state tracking
4. **CoordinateConverter**: Coordinate system transformations
5. **TransformContext**: Transform state management
6. **CSS Styling**: Visual feedback and interaction prevention

### Drag Flow Process

#### 1. Drag Initiation (Mouse Down)

**Event Capture**: ViewInteractionLayer captures mouse down events on nodes
- Only non-root nodes can be dragged (nodes with `parentId`)
- Determines current layout type (custom vs. standard)
- Validates transform state before proceeding

**Initial State Setup**:
```typescript
dragStartRef.current = {
  mouseX: e.clientX,           // Initial screen coordinates
  mouseY: e.clientY,
  nodeX: node.position.x,      // Initial node canvas position
  nodeY: node.position.y,
  mouseWorldX: initialCanvasPos.x,  // Initial canvas coordinates
  mouseWorldY: initialCanvasPos.y,
  initialScale: transform.scale,     // Reference scale
  offsetX: 0,                       // No offset needed
  offsetY: 0
};
```

**Coordinate Transformation**: Screen coordinates are converted to canvas coordinates using CoordinateConverter
- `CoordinateConverter.screenToCanvas(screenX, screenY, transform)`
- Accounts for current scale and offset transformations

**Drag State Activation**:
- `NodeController.startDragging(nodeId, position, isCustomLayout)`
- `MVCGraphLayoutService.setDraggingNode(nodeId, position, isCustomLayout)`
- CSS class `node-dragging` added to document body
- Events emitted: `node:dragging` for real-time edge updates

#### 2. Drag Movement (Mouse Move)

**Position Calculation**: Real-time position updates using CoordinateConverter
```typescript
const nodePosition = CoordinateConverter.calculateNewNodePosition(
  dragStartRef.current.nodeX,      // Initial node position
  dragStartRef.current.nodeY,
  dragStartRef.current.mouseX,     // Initial mouse position
  dragStartRef.current.mouseY,
  e.clientX,                       // Current mouse position
  e.clientY,
  currentTransform                 // Current transform state
);
```

**Layout-Specific Handling**:
- **Custom Layout**: Node position updated directly in NodeStore with `manuallyPositioned: true`
- **Standard Layout**: Position sent to layout service for force-directed positioning
- Layout worker processes dragged nodes with minimal iterations for responsiveness

**Visual Updates**:
- Node position updated in real-time (1:1 cursor tracking)
- Debug visualization updated (when enabled)
- Edge connections re-rendered dynamically

#### 3. Drag Completion (Mouse Up)

**State Cleanup**:
- `NodeController.endDragging(savePosition)`
- `MVCGraphLayoutService.clearDraggingNode(savePosition)`
- CSS class `node-dragging` removed from document body

**Position Finalization**:
- **Custom Layout**: Final position saved with `manuallyPositioned: true`
- **Standard Layout**: Layout algorithm determines final position
- Node relationships updated if dropped on another node (relinking)

### Coordinate System Integration

#### Transform Management

The dragging system uses a sophisticated coordinate transformation system:

**Screen Coordinates**: Browser window coordinates (clientX, clientY)
**Canvas Coordinates**: Virtual space coordinates for node storage
**Transform State**: Scale and offset for viewport transformations

#### CoordinateConverter Functions

1. **screenToCanvas**: Converts screen coordinates to canvas coordinates
   ```typescript
   canvasX = (screenX - offset.x) / scale
   canvasY = (screenY - offset.y) / scale
   ```

2. **calculateCanvasDelta**: Calculates movement delta in canvas space
   ```typescript
   const startCanvas = screenToCanvas(startScreenX, startScreenY, transform);
   const endCanvas = screenToCanvas(endScreenX, endScreenY, transform);
   return { dx: endCanvas.x - startCanvas.x, dy: endCanvas.y - startCanvas.y };
   ```

3. **calculateNewNodePosition**: Determines new node position during drag
   ```typescript
   const canvasDelta = calculateCanvasDelta(initialMouseX, initialMouseY, currentMouseX, currentMouseY, transform);
   return { x: initialNodeX + canvasDelta.dx, y: initialNodeY + canvasDelta.dy };
   ```

### Layout System Integration

#### Custom Layout Mode
- Nodes maintain user-defined positions
- `manuallyPositioned: true` flag prevents automatic repositioning
- Direct position updates to NodeStore
- No layout algorithm interference

#### Standard Layout Mode
- Force-directed positioning with drag constraints
- Dragged nodes temporarily pinned during movement
- Layout worker uses minimal iterations for responsiveness
- Final position determined by layout algorithm

#### Layout Worker Integration
```typescript
// Dragged nodes are marked as pinned in the worker
const isDragging = !!node.isDragging;
const isPinned = !!node.isPinned;

// Worker respects drag state for positioning
if (isDragging) {
  // Pin node at current drag position
  node.fx = node.x;
  node.fy = node.y;
}
```

### Visual Feedback System

#### CSS Classes and Styling

**Global Drag State**: `body.node-dragging`
- Prevents text selection: `user-select: none`
- Disables canvas panning: `.canvas-container { pointer-events: none }`
- Maintains node interactivity: `.node { pointer-events: auto }`

**Individual Node State**: `.node.dragging`
- Visual elevation: `transform: scale(1.02)`
- Enhanced shadow: `box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3)`
- Reduced opacity: `opacity: 0.9`
- High z-index: `z-index: 1000`
- Disabled transitions: `transition: none`

#### Cursor Management
- **Default**: `cursor: grab`
- **Dragging**: `cursor: grabbing`
- **Root Node**: `cursor: default` (not draggable)

### Event System Integration

#### Event Emission
- `node:dragging`: Emitted during drag for real-time edge updates
- `node:position:updated`: Emitted when position changes
- `layout:completed`: Emitted after layout calculations

#### Event Prevention
- Canvas panning disabled during node drag
- Zoom events ignored during drag
- Modal interactions prevented during drag

### Performance Optimizations

#### Throttling and Debouncing
- Mouse move events processed at 60fps maximum
- Layout calculations use minimal iterations during drag
- Debug logging throttled to prevent console spam

#### Efficient Rendering
- Direct DOM updates for node positions
- Canvas edge renderer updates only when necessary
- Transform calculations cached and reused

#### Memory Management
- Event listeners properly cleaned up
- References cleared after drag completion
- Animation frames cancelled when appropriate

### Error Handling and Validation

#### Transform Validation
- Scale values checked for validity (not NaN, not zero)
- Fallback to default transform if invalid
- Extensive logging for debugging coordinate issues

#### Node Validation
- Root node drag prevention
- Node existence verification before operations
- Graceful handling of missing nodes

#### Layout Validation
- Layout type verification before operations
- Fallback to default layout if invalid
- Error recovery for failed layout calculations

### Debug and Development Features

#### Coordinate Debug Visualization
- Real-time coordinate display (disabled in production)
- Transform state monitoring
- Position calculation verification

#### Extensive Logging
- Drag protocol logging with numbered steps
- Coordinate transformation logging
- Layout integration logging
- Performance timing measurements

This comprehensive dragging system ensures smooth, responsive node movement while maintaining proper coordinate transformations, layout integration, and visual feedback across all interaction modes.

## Context System

The application uses React Context for component communication:

1. **NodeInteractionContext**:
   - Manages node interaction between components
   - Provides openNodeEditor and setOpenNodeEditorHandler functions
   - Uses useRef for stable handler references
   - Replaces global window functions with proper React patterns
   - Located in `/src/context/NodeInteractionContext.tsx`

2. **HighlightedNodeContext**:
   - Manages node highlighting for hover-based hit detection
   - Provides highlightedNodeId and registerHighlightedNode functions
   - Improves hit detection accuracy for node interactions
   - Located in `/src/context/HighlightedNodeContext.tsx`

3. **TransformContext**:
   - Centralizes transform state management (scale, offset)
   - Provides access to transform operations through useTransform hook
   - Wraps TransformController functionality in a React-friendly way
   - Enables components to access transform state without direct dependency
   - Located in `/src/context/TransformContext.tsx`
   - Used by GraphBridge, Canvas, MVCGridBackground, and ViewInteractionLayer
   - Supports both direct transform application and animated transitions
   - See [GraphBridge_Refactor.md](GraphBridge_Refactor.md) for implementation details

For more detailed information about the Context System, see [Context_System.md](Context_System.md).

## Keyboard Shortcuts

The application supports the following keyboard shortcuts:

1. **Ctrl+Z**: Undo
2. **Ctrl+Y**: Redo
3. **Ctrl+S**: Save
4. **Ctrl+F**: Search
5. **Ctrl+N**: New node
6. **Delete**: Delete selected node
7. **Escape**: Close modal or cancel operation
8. **Arrow Keys**: Navigate between nodes
9. **Ctrl+Arrow Keys**: Move selected node
10. **Ctrl+Shift+Arrow Keys**: Resize selected node

## Appendix: Feature Reference

For a comprehensive demonstration of all features and components, refer to the [demo-template.json](public/assets/demo-template.json) file, which contains examples of all node types, components, and their properties.

## Implementation Details

### Node Components

The demo-template.json file showcases all available node components:

1. **Rich Text Component**
   - Supports formatted text with HTML styling
   - Supports links to other nodes
   - Supports images and other media

2. **Tasks Component**
   - Checklist with completion tracking
   - Task weighting system for prioritization
   - Progress visualization

3. **Deadline Component**
   - Date-based deadline
   - Progress visualization
   - Optional email notifications

4. **Reminder Component**
   - Time-based reminder
   - Recurring or one-time
   - Optional email notifications

5. **Table Component**
   - Structured data in tabular format
   - Column and row management
   - Data sorting and filtering

6. **Image Component**
   - Image upload and display
   - Resizing and positioning
   - Base64 encoding for storage

### User Tiers and Permissions

The application implements different user tiers with varying permissions:

1. **Tier 0 (Dry Run)**
   - No authentication required
   - Data stored in local storage only
   - No project saving to database
   - Basic features only
   - Demo template available

2. **Tier 1 (Free)**
   - Authentication required
   - One project limit
   - Basic collaboration features
   - Import/export functionality
   - No email notifications

3. **Tier 2 (Premium)**
   - Authentication required
   - Multiple projects
   - Advanced collaboration features
   - Email notifications
   - Advanced tag system
   - Priority support

### Notification System

The application includes a notification system for deadlines and reminders:

1. **In-App Notifications**
   - Displayed in the notification center
   - Triggered by approaching deadlines
   - Triggered by reminders

2. **Email Notifications**
   - Sent for approaching deadlines
   - Sent for reminders
   - Configurable frequency and timing

### Collaboration Features

The application supports collaboration through:

1. **Project Sharing**
   - Invite users by email
   - Set permission levels
   - View collaborator activity

2. **Real-Time Updates**
   - Changes propagate to all collaborators
   - Conflict resolution for simultaneous edits
   - User presence indicators

### Export and Import

The application supports comprehensive export and import functionality:

1. **Export Formats**
   - JSON (full project with all data)
   - PDF (visual representation)
   - Image (screenshot of current view)

2. **Import Sources**
   - JSON files (from previous exports)
   - Other project management tools (limited support)

## Legacy System Removal

The following legacy components are scheduled for removal:

1. **NodeView.tsx**: Legacy node rendering component
2. **PowerManager.tsx**: Legacy power manager component
3. **Conditional code in MVCPowerManager.tsx**: Code that references legacy implementation

The removal process should follow these steps:

1. Ensure all functionality is implemented in the MVC architecture
2. Remove references to legacy components
3. Remove the legacy components themselves
4. Update imports and clean up unused code
5. Test thoroughly to ensure no functionality is lost

Note: GraphBridge.tsx is no longer scheduled for removal. It has been refactored to serve as a container for the TransformProvider and related components. See [GraphBridge_Refactor.md](GraphBridge_Refactor.md) for details.

## Current System State

### Implemented Features

The following features are fully implemented in the MVC architecture:

1. **Node System**
   - Node creation, editing, and deletion
   - Node relinking and hierarchical organization
   - Node completion and color management
   - Node components (rich text, tasks, deadline, reminder, table, image)

2. **User Interface**
   - Graph view with panning and zooming
   - Kanban view for tag-based organization
   - Node editor with component management
   - Quick search and filtering
   - Tag management

3. **Data Management**
   - Supabase integration for data storage
   - Local storage backup
   - Import/export functionality
   - Auto-save after 2 seconds of inactivity

4. **Authentication**
   - Email/password authentication
   - Magic link authentication
   - OAuth providers
   - User tier management

### Partially Implemented Features

The following features are partially implemented and need further development:

1. **Hierarchical ID System**
   - Basic implementation complete
   - Issues with ID updates during relinking
   - Need to implement ID history tracking

2. **Collaboration**
   - Basic project sharing implemented
   - Real-time updates need improvement
   - Conflict resolution needs implementation

3. **Notification System**
   - Basic in-app notifications implemented
   - Email notifications need implementation
   - Configuration options need expansion

### Known Issues

The following issues are known and scheduled for resolution:

1. **Node Relinking**
   - Relinked nodes may revert to original positions when adding new nodes
   - Hierarchical IDs may not update correctly after relinking

2. **View Reset**
   - Adding a new node resets the view position and zoom
   - Need to maintain view state during node operations

3. **Legacy Code**
   - Legacy components still present in the codebase
   - Conditional code in MVCPowerManager.tsx references legacy implementation

## Maintenance Guidelines

### Code Organization

The codebase follows these organizational principles:

1. **Directory Structure**
   - `/src/model`: Model layer components
   - `/src/view`: View layer components
   - `/src/controller`: Controller layer components
   - `/src/components`: React components
   - `/src/services`: Service layer components
   - `/src/utils`: Utility functions
   - `/src/types`: TypeScript type definitions
   - `/src/context`: React context providers

2. **Naming Conventions**
   - Model classes: `[Name]Model.ts`
   - Controller classes: `[Name]Controller.ts`
   - View components: `[Name].tsx`
   - Services: `[Name]Service.ts`
   - Utilities: `[name].ts` (camelCase)

3. **File Organization**
   - Each file should have a single responsibility
   - Related functionality should be grouped together
   - Interfaces should be defined in the same file as their implementation

### Development Workflow

When making changes to the codebase, follow these guidelines:

1. **Feature Development**
   - Create a detailed plan before implementation
   - Implement the feature in the MVC architecture
   - Update the master document with the new feature
   - Write tests for the new feature
   - Update any affected documentation

2. **Bug Fixes**
   - Identify the root cause of the bug
   - Fix the bug in the MVC architecture
   - Write tests to prevent regression
   - Update the master document if necessary

3. **Refactoring**
   - Identify the code to be refactored
   - Create a detailed plan for the refactoring
   - Implement the refactoring in small, testable steps
   - Update the master document if necessary

4. **Documentation**
   - Keep the master document up to date
   - Document all new features and changes
   - Update code comments for clarity
   - Maintain API documentation

## Future Development

Planned future developments include:

1. **Enhanced Collaboration**
   - Real-time editing with conflict resolution
   - Comments and discussions on nodes
   - Activity timeline and history

2. **Advanced Visualization**
   - Alternative view modes (timeline, calendar, etc.)
   - Custom node styling and themes
   - Advanced graph layouts

3. **Integration Capabilities**
   - API for third-party integration
   - Webhooks for event notifications
   - Import/export to popular project management tools

4. **Mobile Application**
   - Native mobile experience
   - Offline capabilities
   - Touch-optimized interface

5. **AI Assistance**
   - Automated task organization
   - Content suggestions
   - Deadline and reminder recommendations

---

This document will be continuously updated as the system evolves. For specific implementation details, refer to the referenced documentation files and the codebase itself.
